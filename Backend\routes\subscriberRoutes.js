const express = require("express");
const router = express.Router();
const subscriberController = require("../controllers/subscriberController");
const { protect, restrictTo } = require("../middleware/auth");

// Public routes (no authentication required)
router.post("/subscribe", subscriberController.subscribe);
router.get("/check-status", subscriberController.checkSubscriptionStatus);
router.get("/unsubscribe/:token", subscriberController.unsubscribe);

// Admin routes (authentication and authorization required)
router.get(
  "/list", 
  protect, 
  restrictTo("admin", "staff"), 
  subscriberController.getSubscribers
);

router.get(
  "/stats", 
  protect, 
  restrictTo("admin", "staff"), 
  subscriberController.getSubscriptionStats
);

module.exports = router;
