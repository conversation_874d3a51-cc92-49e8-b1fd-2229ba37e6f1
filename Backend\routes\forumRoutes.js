const express = require("express");
const router = express.Router();
const forumController = require("../controllers/forumController");
const { protect } = require("../middleware/auth");

// Apply authentication middleware to all routes
router.use(protect);

// Forum routes for a specific course
router.get("/course/:courseId", forumController.getCourseForum);
router.post("/course/:courseId/thread", forumController.createThread);
router.post("/course/:courseId/thread/:threadId/reply", forumController.addReply);
router.post("/course/:courseId/thread/:threadId/like", forumController.toggleThreadLike);
router.post("/course/:courseId/thread/:threadId/reply/:replyId/like", forumController.toggleReplyLike);
router.post("/course/:courseId/thread/:threadId/view", forumController.incrementThreadViews);

module.exports = router;
