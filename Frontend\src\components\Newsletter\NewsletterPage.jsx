import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { FaEnvelope, FaUsers, FaChartLine, FaPlus, FaEdit, FaTrash, FaPaperPlane } from 'react-icons/fa';
import api from '../../redux/api';
import './Newsletter.css';

const NewsletterPage = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [newsletters, setNewsletters] = useState([]);
  const [subscribers, setSubscribers] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingNewsletter, setEditingNewsletter] = useState(null);
  const [newNewsletter, setNewNewsletter] = useState({
    title: '',
    content: '',
    type: 'general'
  });

  // State for "See More" functionality
  const [overviewDisplayCount, setOverviewDisplayCount] = useState(5);
  const [newslettersDisplayCount, setNewslettersDisplayCount] = useState(5);

  useEffect(() => {
    fetchData();
  }, [activeTab]);

  // Helper functions for "See More" functionality
  const handleOverviewSeeMore = () => {
    const newCount = Math.min(overviewDisplayCount + 5, newsletters.length);
    setOverviewDisplayCount(newCount);
  };

  const handleOverviewSeeLess = () => {
    setOverviewDisplayCount(5);
  };

  const handleNewslettersSeeMore = () => {
    const newCount = Math.min(newslettersDisplayCount + 5, newsletters.length);
    setNewslettersDisplayCount(newCount);
  };

  const handleNewslettersSeeLess = () => {
    setNewslettersDisplayCount(5);
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      if (activeTab === 'overview' || activeTab === 'newsletters') {
        const newsletterResponse = await api.get('/newsletter');
        setNewsletters(newsletterResponse.data.data.newsletters);

        // Reset display counts when data is fetched
        setOverviewDisplayCount(5);
        setNewslettersDisplayCount(5);
      }

      if (activeTab === 'overview' || activeTab === 'subscribers') {
        const subscriberResponse = await api.get('/newsletter/subscribers/all');
        setSubscribers(subscriberResponse.data.data.subscribers);

        const statsResponse = await api.get('/newsletter/subscribers/stats');
        setStats(statsResponse.data.data);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNewsletter = async (e) => {
    e.preventDefault();
    try {
      await api.post('/newsletter', newNewsletter);
      toast.success('Newsletter created successfully');
      setShowCreateForm(false);
      setNewNewsletter({ title: '', content: '', type: 'general' });
      fetchData();
    } catch (error) {
      console.error('Error creating newsletter:', error);
      toast.error('Failed to create newsletter');
    }
  };

  const handleUpdateNewsletter = async (e) => {
    e.preventDefault();
    try {
      await api.put(`/newsletter/${editingNewsletter._id}`, editingNewsletter);
      toast.success('Newsletter updated successfully');
      setEditingNewsletter(null);
      fetchData();
    } catch (error) {
      console.error('Error updating newsletter:', error);
      toast.error('Failed to update newsletter');
    }
  };

  const handleDeleteNewsletter = async (id) => {
    if (window.confirm('Are you sure you want to delete this newsletter?')) {
      try {
        await api.delete(`/newsletter/${id}`);
        toast.success('Newsletter deleted successfully');
        fetchData();
      } catch (error) {
        console.error('Error deleting newsletter:', error);
        toast.error('Failed to delete newsletter');
      }
    }
  };

  const handleSendNewsletter = async (id) => {
    if (window.confirm('Are you sure you want to send this newsletter to all subscribers?')) {
      try {
        const response = await api.post(`/newsletter/${id}/send`);
        toast.success(response.data.message);
        fetchData();
      } catch (error) {
        console.error('Error sending newsletter:', error);
        toast.error('Failed to send newsletter');
      }
    }
  };

  const renderOverview = () => (
    <div className="newsletter-overview">
      <div className="stats-grid">
        <div className="stat-card">
          <FaUsers className="stat-icon" />
          <div className="stat-content">
            <h3>{stats.total || 0}</h3>
            <p>Total Subscribers</p>
          </div>
        </div>
        <div className="stat-card">
          <FaChartLine className="stat-icon" />
          <div className="stat-content">
            <h3>{stats.active || 0}</h3>
            <p>Active Subscribers</p>
          </div>
        </div>
        <div className="stat-card">
          <FaEnvelope className="stat-icon" />
          <div className="stat-content">
            <h3>{newsletters.filter(n => n.status === 'sent').length}</h3>
            <p>Newsletters Sent</p>
          </div>
        </div>
        <div className="stat-card">
          <FaPlus className="stat-icon" />
          <div className="stat-content">
            <h3>{stats.recentSubscriptions || 0}</h3>
            <p>New This Month</p>
          </div>
        </div>
      </div>

      <div className="recent-activity">
        <h3>Recent Newsletters</h3>
        <div className="newsletter-list">
          {newsletters.slice(0, overviewDisplayCount).map(newsletter => (
            <div key={newsletter._id} className="newsletter-item">
              <div className="newsletter-info">
                <h4>{newsletter.title}</h4>
                <p>Type: {newsletter.type} | Status: {newsletter.status}</p>
                <small>Created: {new Date(newsletter.createdAt).toLocaleDateString()}</small>
              </div>
              <div className="newsletter-actions">
                {newsletter.status === 'draft' && (
                  <button
                    onClick={() => handleSendNewsletter(newsletter._id)}
                    className="btn-send"
                  >
                    <FaPaperPlane /> Send
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* See More / See Less buttons for Overview */}
        {newsletters.length > 5 && (
          <div className="newsletter-pagination">
            {overviewDisplayCount < newsletters.length && (
              <button
                onClick={handleOverviewSeeMore}
                className="btn-see-more"
              >
                See More ({newsletters.length - overviewDisplayCount} remaining)
              </button>
            )}
            {overviewDisplayCount > 5 && (
              <button
                onClick={handleOverviewSeeLess}
                className="btn-see-less"
              >
                See Less
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );

  const renderNewsletters = () => (
    <div className="newsletters-section">
      <div className="section-header">
        <h3>Newsletter Management</h3>
        <button 
          onClick={() => setShowCreateForm(true)}
          className="btn-primary"
        >
          <FaPlus /> Create Newsletter
        </button>
      </div>

      {showCreateForm && (
        <div className="modal-overlay">
          <div className="modal">
            <h4>Create New Newsletter</h4>
            <form onSubmit={handleCreateNewsletter}>
              <div className="form-group">
                <label>Title</label>
                <input
                  type="text"
                  value={newNewsletter.title}
                  onChange={(e) => setNewNewsletter({...newNewsletter, title: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label>Type</label>
                <select
                  value={newNewsletter.type}
                  onChange={(e) => setNewNewsletter({...newNewsletter, type: e.target.value})}
                >
                  <option value="general">General</option>
                  <option value="course">Course</option>
                  <option value="blog">Blog</option>
                  <option value="forum">Forum</option>
                </select>
              </div>
              <div className="form-group">
                <label>Content</label>
                <textarea
                  value={newNewsletter.content}
                  onChange={(e) => setNewNewsletter({...newNewsletter, content: e.target.value})}
                  rows="6"
                  required
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="btn-primary">Create</button>
                <button 
                  type="button" 
                  onClick={() => setShowCreateForm(false)}
                  className="btn-secondary"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {editingNewsletter && (
        <div className="modal-overlay">
          <div className="modal">
            <h4>Edit Newsletter</h4>
            <form onSubmit={handleUpdateNewsletter}>
              <div className="form-group">
                <label>Title</label>
                <input
                  type="text"
                  value={editingNewsletter.title}
                  onChange={(e) => setEditingNewsletter({...editingNewsletter, title: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label>Type</label>
                <select
                  value={editingNewsletter.type}
                  onChange={(e) => setEditingNewsletter({...editingNewsletter, type: e.target.value})}
                >
                  <option value="general">General</option>
                  <option value="course">Course</option>
                  <option value="blog">Blog</option>
                  <option value="forum">Forum</option>
                </select>
              </div>
              <div className="form-group">
                <label>Content</label>
                <textarea
                  value={editingNewsletter.content}
                  onChange={(e) => setEditingNewsletter({...editingNewsletter, content: e.target.value})}
                  rows="6"
                  required
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="btn-primary">Update</button>
                <button
                  type="button"
                  onClick={() => setEditingNewsletter(null)}
                  className="btn-secondary"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="newsletter-list">
        {newsletters.slice(0, newslettersDisplayCount).map(newsletter => (
          <div key={newsletter._id} className="newsletter-item">
            <div className="newsletter-info">
              <h4>{newsletter.title}</h4>
              <p>Type: {newsletter.type} | Status: {newsletter.status}</p>
              <small>Created: {new Date(newsletter.createdAt).toLocaleDateString()}</small>
              {newsletter.sentAt && (
                <small> | Sent: {new Date(newsletter.sentAt).toLocaleDateString()} to {newsletter.sentTo} subscribers</small>
              )}
            </div>
            <div className="newsletter-actions">
              {newsletter.status === 'draft' && (
                <>
                  <button
                    onClick={() => setEditingNewsletter(newsletter)}
                    className="btn-edit"
                  >
                    <FaEdit />
                  </button>
                  <button
                    onClick={() => handleSendNewsletter(newsletter._id)}
                    className="btn-send"
                  >
                    <FaPaperPlane />
                  </button>
                </>
              )}
              <button
                onClick={() => handleDeleteNewsletter(newsletter._id)}
                className="btn-delete"
              >
                <FaTrash />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* See More / See Less buttons for Newsletters */}
      {newsletters.length > 5 && (
        <div className="newsletter-pagination">
          {newslettersDisplayCount < newsletters.length && (
            <button
              onClick={handleNewslettersSeeMore}
              className="btn-see-more"
            >
              See More ({newsletters.length - newslettersDisplayCount} remaining)
            </button>
          )}
          {newslettersDisplayCount > 5 && (
            <button
              onClick={handleNewslettersSeeLess}
              className="btn-see-less"
            >
              See Less
            </button>
          )}
        </div>
      )}
    </div>
  );

  const renderSubscribers = () => (
    <div className="subscribers-section">
      <h3>Subscriber Management</h3>
      <div className="subscriber-list">
        <table>
          <thead>
            <tr>
              <th>Email</th>
              <th>Name</th>
              <th>Status</th>
              <th>Subscribed Date</th>
              <th>Preferences</th>
            </tr>
          </thead>
          <tbody>
            {subscribers.map(subscriber => (
              <tr key={subscriber._id}>
                <td>{subscriber.email}</td>
                <td>{subscriber.name || 'N/A'}</td>
                <td>
                  <span className={`status ${subscriber.isActive ? 'active' : 'inactive'}`}>
                    {subscriber.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>{new Date(subscriber.subscribedAt).toLocaleDateString()}</td>
                <td>
                  <div className="preferences">
                    {subscriber.preferences?.courses && <span className="pref-tag">Courses</span>}
                    {subscriber.preferences?.blogs && <span className="pref-tag">Blogs</span>}
                    {subscriber.preferences?.forums && <span className="pref-tag">Forums</span>}
                    {subscriber.preferences?.general && <span className="pref-tag">General</span>}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className="newsletter-page">
      <div className="page-header">
        <h2>Newsletter Management</h2>
        <p>Manage newsletters and subscribers</p>
      </div>

      <div className="tab-navigation">
        <button 
          className={activeTab === 'overview' ? 'active' : ''}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button 
          className={activeTab === 'newsletters' ? 'active' : ''}
          onClick={() => setActiveTab('newsletters')}
        >
          Newsletters
        </button>
        <button 
          className={activeTab === 'subscribers' ? 'active' : ''}
          onClick={() => setActiveTab('subscribers')}
        >
          Subscribers
        </button>
      </div>

      <div className="tab-content">
        {loading ? (
          <div className="loading">Loading...</div>
        ) : (
          <>
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'newsletters' && renderNewsletters()}
            {activeTab === 'subscribers' && renderSubscribers()}
          </>
        )}
      </div>
    </div>
  );
};

export default NewsletterPage;
