# Newsletter Pagination Implementation

## Overview
This document describes the pagination functionality implemented for the Newsletter screens in the IIM-EDU application.

## Implementation Details

### Components Modified
- **File**: `Frontend/src/components/Newsletter/NewsletterPage.jsx`
- **Sections**: 
  - Recent Newsletters (Overview tab)
  - Newsletter Management (Newsletters tab)

### Functionality Implemented

#### Initial State
- Display exactly 5 newsletters by default
- Show only a "See More" button (when there are more than 5 newsletters)

#### First "See More" Click
- Display 10 newsletters total (5 additional)
- Show both "See More" and "See Less" buttons
- "See More" button appears if there are more newsletters to load

#### Subsequent "See More" Clicks
- Continue adding 5 newsletters at a time
- Always show both "See More" and "See Less" buttons
- If there are no more newsletters to load, hide the "See More" button and show only "See Less"

#### "See Less" Click
- Always reset to display exactly 5 newsletters
- Show only the "See More" button
- Reset the pagination state to initial

### Technical Implementation

#### State Management
```javascript
// State for "See More" functionality
const [overviewDisplayCount, setOverviewDisplayCount] = useState(5);
const [newslettersDisplayCount, setNewslettersDisplayCount] = useState(5);
```

#### Handler Functions
```javascript
// Helper functions for "See More" functionality
const handleOverviewSeeMore = () => {
  setOverviewDisplayCount(overviewDisplayCount + 5);
};

const handleOverviewSeeLess = () => {
  setOverviewDisplayCount(5);
};

const handleNewslettersSeeMore = () => {
  setNewslettersDisplayCount(newslettersDisplayCount + 5);
};

const handleNewslettersSeeLess = () => {
  setNewslettersDisplayCount(5);
};
```

#### Button Rendering Logic
- **See More Button**: Shows when `displayCount < newsletters.length`
- **See Less Button**: Shows when `displayCount > 5`
- Both buttons can appear simultaneously after the first "See More" click

### CSS Styling
- Uses existing styles from `Frontend/src/components/Newsletter/Newsletter.css`
- Button classes: `.btn-see-more`, `.btn-see-less`
- Container class: `.newsletter-pagination`
- Responsive design included for mobile devices

### Key Features
1. **Simple Logic**: Uses increment/decrement approach (no complex calculations)
2. **State Reset**: "See Less" always resets to 5 items
3. **Dynamic Updates**: No page reloads - all changes are dynamic
4. **Consistent Behavior**: Same logic applied to both Overview and Newsletter Management sections
5. **Existing Styling**: Maintains current design and layout

### Testing
To test the functionality:
1. Navigate to Newsletter Management page
2. Switch between Overview and Newsletters tabs
3. Click "See More" to load additional newsletters (5 at a time)
4. Click "See Less" to reset to 5 newsletters
5. Verify button visibility changes based on available content

### Browser Compatibility
- Works with all modern browsers
- Responsive design for mobile and tablet devices
- Uses React hooks (useState) for state management
