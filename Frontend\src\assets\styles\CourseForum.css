/* Course Forum Styles */
.course-forum {
  padding: 20px;
  background: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
}

.forum-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-gray);
}

.forum-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.forum-icon {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.forum-title h3 {
  margin: 0;
  color: var(--text-dark);
  font-size: 1.4rem;
}

.new-thread-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: var(--border-small-radius);
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.new-thread-btn:hover {
  background: var(--primary-dark);
}

/* New Thread Form */
.new-thread-form {
  background: var(--bg-light);
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  padding: 20px;
  margin-bottom: 20px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.form-header h4 {
  margin: 0;
  color: var(--text-dark);
}

.close-form-btn {
  background: none;
  border: none;
  color: var(--text-gray);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 5px;
}

.close-form-btn:hover {
  color: var(--text-dark);
}

.form-group {
  margin-bottom: 15px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-radius);
  font-size: 14px;
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  gap: 15px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: var(--text-dark);
}

.tags-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-radius);
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.form-actions button {
  padding: 10px 20px;
  border: none;
  border-radius: var(--border-small-radius);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.form-actions button[type="button"] {
  background: var(--bg-gray);
  color: var(--text-dark);
}

.form-actions button[type="button"]:hover {
  background: var(--border-gray);
}

.submit-btn {
  background: var(--primary-color);
  color: white;
}

.submit-btn:hover {
  background: var(--primary-hover-color);
}

/* Threads List */
.threads-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.thread-item {
  background: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: var(--border-radius);
  padding: 15px;
  transition: box-shadow 0.2s ease;
}

.thread-item:hover {
  box-shadow: var(--box-shadow-light);
}

.thread-header {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 10px;
}

.thread-meta {
  display: flex;
  gap: 5px;
  margin-top: 2px;
}

.question-icon {
  color: var(--warning-color);
}

.pinned-icon {
  color: var(--error-color);
}

.solved-icon {
  color: var(--success-color);
}

.thread-title {
  margin: 0;
  color: var(--text-dark);
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: color 0.2s ease;
}

.thread-title:hover {
  color: var(--primary-color);
}

.thread-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.thread-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-weight: 500;
  color: var(--text-dark);
}

.author-role {
  color: var(--text-gray);
  font-weight: normal;
  margin-left: 5px;
}

.thread-stats {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 0.9rem;
  color: var(--text-gray);
}

.thread-views {
  display: flex;
  align-items: center;
  gap: 4px;
}

.thread-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.thread-tag {
  background: var(--bg-light);
  color: var(--text-dark);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  border: 1px solid var(--border-gray);
}

/* Thread Details */
.thread-details {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--border-gray);
}

.thread-content {
  margin-bottom: 15px;
}

.thread-content p {
  margin-bottom: 10px;
  line-height: 1.6;
  color: var(--text-dark);
}

.thread-actions {
  display: flex;
  gap: 10px;
}

.like-btn,
.reply-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background: none;
  border: 1px solid var(--border-gray);
  padding: 6px 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.like-btn:hover,
.reply-btn:hover {
  background: var(--bg-light);
}

.like-btn.liked {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Reply Form */
.reply-form {
  background: var(--bg-light);
  padding: 15px;
  border-radius: var(--border-radius);
  margin-bottom: 15px;
}

.reply-form textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-radius);
  resize: vertical;
  margin-bottom: 10px;
}

.reply-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.reply-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.9rem;
}

.reply-actions button:first-child {
  background: var(--bg-gray);
  color: var(--text-dark);
}

.submit-reply-btn {
  background: var(--primary-color);
  color: white;
}

/* Replies */
.replies-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reply-item {
  background: var(--bg-light);
  padding: 12px;
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary-color);
}

.reply-author {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.reply-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.reply-author-name {
  font-weight: 500;
  color: var(--text-dark);
  font-size: 0.9rem;
}

.reply-author-role {
  color: var(--text-gray);
  font-weight: normal;
  margin-left: 5px;
}

.reply-date {
  color: var(--text-gray);
  font-size: 0.8rem;
  margin-left: auto;
}

.reply-content p {
  margin: 0;
  line-height: 1.5;
  color: var(--text-dark);
}

.answer-badge {
  display: inline-block;
  background: var(--success-color);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-top: 5px;
}

/* No Threads State */
.no-threads {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-gray);
}

.no-threads-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--border-gray);
}

.no-threads h4 {
  margin: 0 0 10px 0;
  color: var(--text-dark);
}

.no-threads p {
  margin: 0 0 20px 0;
}

.start-discussion-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-small-radius);
  cursor: pointer;
  font-weight: 500;
}

.start-discussion-btn:hover {
  background: var(--primary-hover-color);
}

/* Loading State */
.forum-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-gray);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-gray);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Forum Filters */
.forum-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  

  border-radius: var(--border-radius);
  gap: 20px;
}

.category-filters {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--bg-white);
  border: 2px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  color: var(--text-gray);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--smallfont);
  font-weight: 500;
}

.category-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.category-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.category-icon {
  font-size: 0.9rem;
}

.sort-options {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sort-select {
  padding: 8px 12px;
  border: 2px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  background: var(--bg-white);
  color: var(--text-color);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.sort-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Category Select in Form */
.category-select {
  padding: 10px 12px;
  border: 2px solid var(--border-gray);
  border-radius: var(--border-radius);
  background: var(--bg-white);
  color: var(--text-color);
  font-size: var(--basefont);
  cursor: pointer;
  transition: border-color 0.3s ease;
  width: 100%;
}

.category-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Enhanced Thread Items */
.thread-item {
  position: relative;
  transition: all 0.3s ease;
}

.thread-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: transparent;
  border-radius: 0 4px 4px 0;
  transition: background 0.3s ease;
}

.thread-item:hover::before {
  background: var(--primary-color);
}

.thread-item.question::before {
  background: var(--warning-color);
}

.thread-item.solved::before {
  background: var(--success-color);
}

/* Thread Category Badge */
.thread-category {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: var(--primary-light-bg);
  color: var(--primary-color);
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 500;
  margin-left: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .forum-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .forum-filters {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .category-filters {
    justify-content: center;
  }

  .category-btn {
    flex: 1;
    justify-content: center;
    min-width: 120px;
  }

  .sort-options {
    justify-content: center;
  }

  .thread-info {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .thread-stats {
    gap: 10px;
  }

  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .tags-input {
    width: 100%;
  }

  .thread-actions {
    flex-wrap: wrap;
  }

  .thread-category {
    margin-left: 0;
    margin-top: 5px;
  }
}
