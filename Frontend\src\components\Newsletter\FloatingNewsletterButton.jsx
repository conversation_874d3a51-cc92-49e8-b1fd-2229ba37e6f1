import React, { useState } from 'react';
import { FaEnvelope, FaTimes } from 'react-icons/fa';
import NewsletterModal from './NewsletterModal';

const FloatingNewsletterButton = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      {/* Floating Newsletter Button */}
      <div 
        className="floating-newsletter-button"
        onClick={handleOpenModal}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="floating-button-icon">
          <FaEnvelope />
        </div>
        
        {/* Tooltip */}
        <div className={`floating-button-tooltip ${isHovered ? 'visible' : ''}`}>
          Subscribe to Newsletter
        </div>
      </div>

      {/* Newsletter Modal */}
      <NewsletterModal 
        isOpen={isModalOpen} 
        onClose={handleCloseModal} 
      />
    </>
  );
};

export default FloatingNewsletterButton;
