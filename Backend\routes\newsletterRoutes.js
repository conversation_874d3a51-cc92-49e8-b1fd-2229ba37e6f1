const express = require('express');
const router = express.Router();
const newsletterController = require('../controllers/newsletterController');
const { protect, restrictTo } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(protect);

// Newsletter management routes (admin only)
router.route('/')
  .get(restrictTo('admin'), newsletterController.getAllNewsletters)
  .post(restrictTo('admin'), newsletterController.createNewsletter);

router.route('/:id')
  .get(restrictTo('admin'), newsletterController.getNewsletter)
  .put(restrictTo('admin'), newsletterController.updateNewsletter)
  .delete(restrictTo('admin'), newsletterController.deleteNewsletter);

// Send newsletter route
router.post('/:id/send', restrictTo('admin'), newsletterController.sendNewsletter);

// Subscriber management routes (admin only)
router.get('/subscribers/all', restrictTo('admin'), newsletterController.getAllSubscribers);
router.get('/subscribers/stats', restrictTo('admin'), newsletterController.getSubscriberStats);

module.exports = router;
