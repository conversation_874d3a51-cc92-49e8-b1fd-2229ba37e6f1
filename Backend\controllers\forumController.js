const Forum = require("../models/Forum");
const Course = require("../models/Course");
const User = require("../models/User");

// Get forum for a specific course
exports.getCourseForum = async (req, res) => {
  try {
    const { courseId } = req.params;

    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({ message: "Course not found" });
    }

    // Find or create forum for the course
    let forum = await Forum.findOne({ course: courseId })
      .populate({
        path: "threads.author",
        select: "name firstName lastName profile.avatar role",
      })
      .populate({
        path: "threads.replies.author",
        select: "name firstName lastName profile.avatar role",
      });

    if (!forum) {
      forum = new Forum({ course: courseId, threads: [] });
      await forum.save();
    }

    res.json(forum);
  } catch (error) {
    console.error("Error retrieving course forum:", error);
    res.status(500).json({
      message: "Error retrieving course forum",
      error: error.message,
    });
  }
};

// Create a new thread
exports.createThread = async (req, res) => {
  try {
    const { courseId } = req.params;
    const { title, content, isQuestion, category, tags } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!title || !title.trim() || !content || !content.trim()) {
      return res.status(400).json({ message: "Title and content are required" });
    }

    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({ message: "Course not found" });
    }

    // Find or create forum for the course
    let forum = await Forum.findOne({ course: courseId });
    if (!forum) {
      forum = new Forum({ course: courseId, threads: [] });
    }

    // Create new thread
    const newThread = {
      title: title.trim(),
      content: content.trim(),
      author: userId,
      isQuestion: isQuestion || false,
      category: category || 'general',
      tags: tags || [],
      createdAt: new Date(),
      updatedAt: new Date(),
      replies: [],
      likes: [],
      views: 0,
    };

    forum.threads.push(newThread);
    await forum.save();

    // Return the updated forum with populated data
    const updatedForum = await Forum.findOne({ course: courseId })
      .populate({
        path: "threads.author",
        select: "name firstName lastName profile.avatar role",
      })
      .populate({
        path: "threads.replies.author",
        select: "name firstName lastName profile.avatar role",
      });

    // Create and send newsletter for important forum discussions (questions only)
    if (isQuestion) {
      try {
        const { autoCreateNewsletter } = require('./newsletterController');
        await autoCreateNewsletter('forum', {
          _id: newThread._id || forum.threads[forum.threads.length - 1]._id,
          title: newThread.title,
          author: userId
        });
      } catch (newsletterError) {
        console.error("Newsletter creation error:", newsletterError);
        // Don't fail the thread creation if newsletter fails
      }
    }

    res.status(201).json({
      message: "Thread created successfully",
      forum: updatedForum,
    });
  } catch (error) {
    console.error("Error creating thread:", error);
    res.status(500).json({
      message: "Error creating thread",
      error: error.message,
    });
  }
};

// Add a reply to a thread
exports.addReply = async (req, res) => {
  try {
    const { courseId, threadId } = req.params;
    const { content, isAnswer } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!content || !content.trim()) {
      return res.status(400).json({ message: "Reply content is required" });
    }

    const forum = await Forum.findOne({ course: courseId });
    if (!forum) {
      return res.status(404).json({ message: "Forum not found" });
    }

    const thread = forum.threads.id(threadId);
    if (!thread) {
      return res.status(404).json({ message: "Thread not found" });
    }

    // Create new reply
    const newReply = {
      content: content.trim(),
      author: userId,
      isAnswer: isAnswer || false,
      createdAt: new Date(),
      updatedAt: new Date(),
      likes: [],
    };

    thread.replies.push(newReply);
    thread.updatedAt = new Date();

    // If this is marked as an answer and the thread is a question, mark as solved
    if (isAnswer && thread.isQuestion) {
      thread.isSolved = true;
    }

    await forum.save();

    // Return the updated forum with populated data
    const updatedForum = await Forum.findOne({ course: courseId })
      .populate({
        path: "threads.author",
        select: "name firstName lastName profile.avatar role",
      })
      .populate({
        path: "threads.replies.author",
        select: "name firstName lastName profile.avatar role",
      });

    res.status(201).json({
      message: "Reply added successfully",
      forum: updatedForum,
    });
  } catch (error) {
    console.error("Error adding reply:", error);
    res.status(500).json({
      message: "Error adding reply",
      error: error.message,
    });
  }
};

// Like/Unlike a thread
exports.toggleThreadLike = async (req, res) => {
  try {
    const { courseId, threadId } = req.params;
    const userId = req.user.id;

    const forum = await Forum.findOne({ course: courseId });
    if (!forum) {
      return res.status(404).json({ message: "Forum not found" });
    }

    const thread = forum.threads.id(threadId);
    if (!thread) {
      return res.status(404).json({ message: "Thread not found" });
    }

    // Check if user already liked the thread
    const existingLike = thread.likes.find(
      (like) => like.user.toString() === userId
    );

    if (existingLike) {
      // Remove like
      thread.likes.pull(existingLike._id);
    } else {
      // Add like
      thread.likes.push({ user: userId });
    }

    await forum.save();

    res.json({
      message: existingLike ? "Like removed" : "Like added",
      likesCount: thread.likes.length,
    });
  } catch (error) {
    console.error("Error toggling thread like:", error);
    res.status(500).json({
      message: "Error toggling thread like",
      error: error.message,
    });
  }
};

// Like/Unlike a reply
exports.toggleReplyLike = async (req, res) => {
  try {
    const { courseId, threadId, replyId } = req.params;
    const userId = req.user.id;

    const forum = await Forum.findOne({ course: courseId });
    if (!forum) {
      return res.status(404).json({ message: "Forum not found" });
    }

    const thread = forum.threads.id(threadId);
    if (!thread) {
      return res.status(404).json({ message: "Thread not found" });
    }

    const reply = thread.replies.id(replyId);
    if (!reply) {
      return res.status(404).json({ message: "Reply not found" });
    }

    // Check if user already liked the reply
    const existingLike = reply.likes.find(
      (like) => like.user.toString() === userId
    );

    if (existingLike) {
      // Remove like
      reply.likes.pull(existingLike._id);
    } else {
      // Add like
      reply.likes.push({ user: userId });
    }

    await forum.save();

    res.json({
      message: existingLike ? "Like removed" : "Like added",
      likesCount: reply.likes.length,
    });
  } catch (error) {
    console.error("Error toggling reply like:", error);
    res.status(500).json({
      message: "Error toggling reply like",
      error: error.message,
    });
  }
};

// Increment thread views
exports.incrementThreadViews = async (req, res) => {
  try {
    const { courseId, threadId } = req.params;

    const forum = await Forum.findOne({ course: courseId });
    if (!forum) {
      return res.status(404).json({ message: "Forum not found" });
    }

    const thread = forum.threads.id(threadId);
    if (!thread) {
      return res.status(404).json({ message: "Thread not found" });
    }

    thread.views += 1;
    await forum.save();

    res.json({ message: "Views incremented", views: thread.views });
  } catch (error) {
    console.error("Error incrementing thread views:", error);
    res.status(500).json({
      message: "Error incrementing thread views",
      error: error.message,
    });
  }
};
