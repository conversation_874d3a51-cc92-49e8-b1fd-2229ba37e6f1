import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { FaEnvelope, FaCheck, FaTimes } from 'react-icons/fa';
import api from '../../redux/api';
import './NewsletterSubscription.css';

const NewsletterSubscription = () => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [preferences, setPreferences] = useState({
    courses: true,
    blogs: true,
    forums: true,
    general: true
  });
  const [loading, setLoading] = useState(false);
  const [subscribed, setSubscribed] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast.error('Email is required');
      return;
    }

    setLoading(true);
    try {
      await api.post('/subscriber/subscribe', {
        email: email.trim(),
        name: name.trim() || undefined,
        preferences
      });
      
      setSubscribed(true);
      toast.success('Successfully subscribed to our newsletter!');
      setEmail('');
      setName('');
    } catch (error) {
      console.error('Subscription error:', error);
      if (error.response?.status === 409) {
        toast.error('This email is already subscribed');
      } else {
        toast.error('Failed to subscribe. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePreferenceChange = (type) => {
    setPreferences(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  if (subscribed) {
    return (
      <div className="newsletter-subscription success">
        <div className="success-content">
          <FaCheck className="success-icon" />
          <h3>Welcome to our newsletter!</h3>
          <p>Thank you for subscribing. You'll receive updates based on your preferences.</p>
          <button 
            onClick={() => setSubscribed(false)}
            className="btn-secondary"
          >
            Subscribe Another Email
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="newsletter-subscription">
      <div className="subscription-header">
        <FaEnvelope className="newsletter-icon" />
        <h3>Stay Updated with IIM Education</h3>
        <p>Get the latest updates on courses, blogs, and educational content</p>
      </div>

      <form onSubmit={handleSubmit} className="subscription-form">
        <div className="form-group">
          <input
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={loading}
          />
        </div>

        <div className="form-group">
          <input
            type="text"
            placeholder="Your name (optional)"
            value={name}
            onChange={(e) => setName(e.target.value)}
            disabled={loading}
          />
        </div>

        <div className="preferences-section">
          <h4>What you'll get:</h4>
          <div className="preferences-grid">
            <label className="preference-item">
              <input
                type="checkbox"
                checked={preferences.courses}
                onChange={() => handlePreferenceChange('courses')}
                disabled={loading}
              />
              <span className="checkmark"></span>
              <div className="preference-content">
                <strong>New Courses</strong>
                <small>Get notified when new courses are available</small>
              </div>
            </label>

            <label className="preference-item">
              <input
                type="checkbox"
                checked={preferences.blogs}
                onChange={() => handlePreferenceChange('blogs')}
                disabled={loading}
              />
              <span className="checkmark"></span>
              <div className="preference-content">
                <strong>Blog Posts</strong>
                <small>Latest educational articles and insights</small>
              </div>
            </label>

            <label className="preference-item">
              <input
                type="checkbox"
                checked={preferences.forums}
                onChange={() => handlePreferenceChange('forums')}
                disabled={loading}
              />
              <span className="checkmark"></span>
              <div className="preference-content">
                <strong>Forum Discussions</strong>
                <small>Important discussions and Q&A sessions</small>
              </div>
            </label>

            <label className="preference-item">
              <input
                type="checkbox"
                checked={preferences.general}
                onChange={() => handlePreferenceChange('general')}
                disabled={loading}
              />
              <span className="checkmark"></span>
              <div className="preference-content">
                <strong>General Updates</strong>
                <small>Platform updates and announcements</small>
              </div>
            </label>
          </div>
        </div>

        <button 
          type="submit" 
          className="subscribe-btn"
          disabled={loading || !Object.values(preferences).some(Boolean)}
        >
          {loading ? 'Subscribing...' : 'Subscribe to Newsletter'}
        </button>

        <p className="privacy-note">
          We respect your privacy. You can unsubscribe at any time.
        </p>
      </form>
    </div>
  );
};

export default NewsletterSubscription;
