.newsletter-subscription {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  margin: 20px 0;
}

.newsletter-subscription.success {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  text-align: center;
}

.subscription-header {
  text-align: center;
  margin-bottom: 25px;
}

.newsletter-icon {
  font-size: 32px;
  margin-bottom: 15px;
  opacity: 0.9;
}

.subscription-header h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.subscription-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.subscription-form {
  max-width: 400px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  background: rgba(255,255,255,0.9);
  color: #333;
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  background: white;
  box-shadow: 0 0 0 3px rgba(255,255,255,0.3);
}

.form-group input::placeholder {
  color: #666;
}

.form-group input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.preferences-section {
  margin: 25px 0;
}

.preferences-section h4 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
}

.preferences-grid {
  display: grid;
  gap: 12px;
}

.preference-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255,255,255,0.1);
  transition: all 0.3s ease;
}

.preference-item:hover {
  background: rgba(255,255,255,0.15);
}

.preference-item input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255,255,255,0.7);
  border-radius: 4px;
  position: relative;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.preference-item input[type="checkbox"]:checked + .checkmark {
  background: white;
  border-color: white;
}

.preference-item input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #667eea;
  font-weight: bold;
  font-size: 14px;
}

.preference-content {
  flex: 1;
}

.preference-content strong {
  display: block;
  margin-bottom: 4px;
  font-size: 16px;
}

.preference-content small {
  opacity: 0.8;
  font-size: 14px;
  line-height: 1.3;
}

.subscribe-btn {
  width: 100%;
  padding: 14px 20px;
  border: none;
  border-radius: 8px;
  background: white;
  color: #667eea;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 15px;
}

.subscribe-btn:hover:not(:disabled) {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.subscribe-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.privacy-note {
  text-align: center;
  font-size: 12px;
  opacity: 0.8;
  margin: 0;
  line-height: 1.4;
}

.success-content {
  padding: 20px;
}

.success-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.success-content h3 {
  margin: 0 0 15px 0;
  font-size: 28px;
  font-weight: 600;
}

.success-content p {
  margin: 0 0 25px 0;
  font-size: 16px;
  opacity: 0.9;
}

.btn-secondary {
  padding: 12px 24px;
  border: 2px solid white;
  border-radius: 8px;
  background: transparent;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: white;
  color: #4CAF50;
}

@media (max-width: 768px) {
  .newsletter-subscription {
    padding: 20px;
    margin: 15px 0;
  }
  
  .subscription-header h3 {
    font-size: 20px;
  }
  
  .subscription-header p {
    font-size: 14px;
  }
  
  .newsletter-icon {
    font-size: 28px;
  }
  
  .form-group input {
    padding: 10px 14px;
    font-size: 14px;
  }
  
  .preference-item {
    padding: 10px;
  }
  
  .preference-content strong {
    font-size: 14px;
  }
  
  .preference-content small {
    font-size: 12px;
  }
  
  .subscribe-btn {
    padding: 12px 16px;
    font-size: 14px;
  }
}
