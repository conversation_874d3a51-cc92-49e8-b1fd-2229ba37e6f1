# Frontend Environment Variables
# Copy this file to .env and update the values according to your setup

# API Configuration
VITE_API_URL=http://localhost:5000/api

# Application Configuration
VITE_APP_NAME=IIM Education Platform
VITE_APP_VERSION=1.0.0

# Development Configuration
VITE_NODE_ENV=development

# Optional: Analytics and Tracking
# VITE_GOOGLE_ANALYTICS_ID=your_google_analytics_id
# VITE_HOTJAR_ID=your_hotjar_id

# Optional: Third-party Services
# VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
# VITE_PAYPAL_CLIENT_ID=your_paypal_client_id

# Optional: Social Media Integration
# VITE_FACEBOOK_APP_ID=your_facebook_app_id
# VITE_GOOGLE_CLIENT_ID=your_google_client_id

# Optional: CDN and Asset URLs
# VITE_CDN_URL=https://your-cdn-url.com
# VITE_ASSETS_URL=https://your-assets-url.com

# Optional: Feature Flags
VITE_ENABLE_NEWSLETTER=true
VITE_ENABLE_FORUMS=true
VITE_ENABLE_CERTIFICATES=true
VITE_ENABLE_QUIZZES=true

# Optional: Debug and Logging
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
