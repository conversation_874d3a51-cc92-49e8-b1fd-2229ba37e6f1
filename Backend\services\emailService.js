const nodemailer = require('nodemailer');
const Subscriber = require('../models/Subscriber');
const Newsletter = require('../models/Newsletter');
const AppError = require('../utils/appError');

// Create reusable transporter
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Send a single email
const sendEmail = async (options) => {
  try {
    const mailOptions = {
      from: `IIM Education <${process.env.EMAIL_USER}>`,
      to: options.email,
      subject: options.subject,
      html: options.html,
    };

    const info = await transporter.sendMail(mailOptions);
    return info;
  } catch (error) {
    console.error('Email sending error:', error);
    throw new AppError('Failed to send email', 500);
  }
};

// Send newsletter to all active subscribers based on their preferences
const sendNewsletterToSubscribers = async (newsletterId) => {
  try {
    // Get newsletter details
    const newsletter = await Newsletter.findById(newsletterId);
    if (!newsletter) {
      throw new AppError('Newsletter not found', 404);
    }

    // Get all active subscribers with matching preferences
    const preferenceField = `preferences.${newsletter.type === 'course' ? 'courses' : 
                            newsletter.type === 'blog' ? 'blogs' : 
                            newsletter.type === 'forum' ? 'forums' : 'general'}`;
    
    const query = { 
      isActive: true, 
      status: 1,
      [preferenceField]: true
    };

    const subscribers = await Subscriber.find(query);
    
    if (subscribers.length === 0) {
      return { success: true, sent: 0, message: 'No active subscribers found with matching preferences' };
    }

    // Send emails in batches to avoid rate limits
    const batchSize = 50;
    let sentCount = 0;

    for (let i = 0; i < subscribers.length; i += batchSize) {
      const batch = subscribers.slice(i, i + batchSize);
      
      // Send emails in parallel for each batch
      const emailPromises = batch.map(subscriber => {
        const unsubscribeUrl = `${process.env.FRONTEND_URL}/unsubscribe?token=${subscriber.unsubscribeToken}`;
        
        return sendEmail({
          email: subscriber.email,
          subject: newsletter.title,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2>${newsletter.title}</h2>
              <div>${newsletter.content}</div>
              <hr style="margin: 20px 0;">
              <p style="font-size: 12px; color: #666;">
                You're receiving this email because you subscribed to IIM Education newsletters.
                <br>
                <a href="${unsubscribeUrl}">Unsubscribe</a> if you no longer wish to receive these emails.
              </p>
            </div>
          `
        });
      });

      // Wait for all emails in the current batch to be sent
      await Promise.all(emailPromises);
      sentCount += batch.length;
      
      // Add a small delay between batches to avoid rate limits
      if (i + batchSize < subscribers.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Update newsletter status
    newsletter.status = 'sent';
    newsletter.sentAt = new Date();
    newsletter.sentTo = sentCount;
    await newsletter.save();

    return { success: true, sent: sentCount };
  } catch (error) {
    console.error('Newsletter sending error:', error);
    throw new AppError(`Failed to send newsletter: ${error.message}`, 500);
  }
};

module.exports = {
  sendEmail,
  sendNewsletterToSubscribers
};
