/* Newsletter Subscription Styles */
.newsletter-subscription {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  box-shadow: var(--box-shadow-light);
  position: relative;
  overflow: hidden;
}

.newsletter-subscription::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.newsletter-subscription > * {
  position: relative;
  z-index: 1;
}

.newsletter-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.newsletter-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.newsletter-icon svg {
  font-size: 1.2rem;
  color: white;
}

.newsletter-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: white;
}

.newsletter-description {
  margin: 0 0 20px 0;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
}

/* Form Styles */
.newsletter-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 12px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  color: var(--text-gray);
  font-size: 0.9rem;
  z-index: 2;
}

.newsletter-input {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.email-input {
  padding-left: 40px;
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.newsletter-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.newsletter-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.newsletter-submit-btn {
  width: 100%;
  background: white;
  color: var(--primary-color);
  border: none;
  padding: 12px 16px;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.newsletter-submit-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.newsletter-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.submit-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--primary-color);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Benefits Section */
.newsletter-benefits {
  margin-bottom: 15px;
}

.newsletter-benefits h4 {
  margin: 0 0 10px 0;
  font-size: 1rem;
  color: white;
  font-weight: 600;
}

.newsletter-benefits ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.newsletter-benefits li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.benefit-icon {
  color: #4ade80;
  font-size: 0.8rem;
  flex-shrink: 0;
}

.newsletter-privacy {
  margin: 0;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  font-style: italic;
}

/* Success State */
.newsletter-success {
  text-align: center;
  padding: 10px 0;
}

.success-icon {
  background: #4ade80;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px auto;
}

.success-icon svg {
  font-size: 1.5rem;
  color: white;
}

.newsletter-success h4 {
  margin: 0 0 10px 0;
  font-size: 1.2rem;
  color: white;
  font-weight: 600;
}

.newsletter-success p {
  margin: 0 0 20px 0;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
}

.newsletter-reset-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.newsletter-reset-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .newsletter-subscription {
    padding: 15px;
    margin-bottom: 15px;
  }

  .newsletter-header h3 {
    font-size: 1.2rem;
  }

  .newsletter-description {
    font-size: 0.9rem;
  }

  .newsletter-input {
    padding: 10px;
    font-size: 13px;
  }

  .email-input {
    padding-left: 35px;
  }

  .newsletter-submit-btn {
    padding: 10px 14px;
    font-size: 13px;
  }

  .newsletter-benefits li {
    font-size: 0.85rem;
  }

  .newsletter-privacy {
    font-size: 0.75rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .newsletter-subscription {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  }
}

/* Animation for form appearance */
.newsletter-form {
  animation: slideInUp 0.3s ease-out;
}

.newsletter-success {
  animation: fadeIn 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
