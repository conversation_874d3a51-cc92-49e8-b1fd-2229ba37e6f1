const mongoose = require("mongoose");

const subscriberSchema = new mongoose.Schema(
  {
    email: { 
      type: String, 
      required: true, 
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please provide a valid email address']
    },
    name: { 
      type: String,
      trim: true
    },
    isActive: { 
      type: Boolean, 
      default: true 
    },
    subscribedAt: { 
      type: Date, 
      default: Date.now 
    },
    unsubscribedAt: { 
      type: Date 
    },
    unsubscribeToken: {
      type: String,
      unique: true
    },
    preferences: {
      courses: { type: Boolean, default: true },
      blogs: { type: Boolean, default: true },
      forums: { type: Boolean, default: true },
      general: { type: Boolean, default: true },
    },
    status: {
      type: Number,
      default: 1 // 1: active, 0: inactive (soft delete)
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("Subscriber", subscriberSchema);
